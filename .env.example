# Dify API配置
NEXT_PUBLIC_DIFY_API_KEY=your_dify_api_key_here

# 百炼平台API配置
DASHSCOPE_API_KEY=your_dashscope_api_key_here
BAILIAN_APP_ID=your_bailian_app_id_here

# 百炼平台API端点（可选，使用默认值）
# BAILIAN_BASE_URL=https://dashscope.aliyuncs.com/api/v1/apps

# API调用配置（可选）
# BAILIAN_TIMEOUT=30000
# BAILIAN_MAX_RETRIES=3
# BAILIAN_RETRY_DELAY=1000

# 使用说明：
# 1. 复制此文件为 .env
# 2. 将 your_dashscope_api_key_here 替换为您的百炼平台API密钥
# 3. 将 your_bailian_app_id_here 替换为您的百炼平台应用ID
# 4. 根据需要调整其他配置项
